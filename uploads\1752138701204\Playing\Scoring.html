<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US">
<head>
    <title>Scoring</title>
    <style type="text/css" media="screen">
		@import url( ../shared/style.css );
	</style>
	<script src="../shared/scormfunctions.js" type="text/javascript"></script>
	<script src="../shared/contentfunctions.js" type="text/javascript"></script>
</head>
<body>
    <h1>Scoring</h1>
    <img src="scoring.jpg" id="golfimg" alt="golfing image" />
    <p>In every form of play, the goal is to play as few strokes per round as possible. Scores for each hole can be described as follows:</p>
<table>
<tbody>
<tr>
<th>Term on a<br/>Scoreboard
</th>
<th>Specific Term</th>
<th>Definition</th>
</tr>
<tr>
<td class="rowheader">-4</td>
<td>
Triple Eagle (or Condor)
</td>
<td>four strokes under par</td>
</tr>
<tr>
<td class="rowheader">-3</td>
<td>
Double Eagle (or Albatross)
</td>
<td>three strokes under par</td>
</tr>
<tr>
<td class="rowheader">-2</td>
<td>
Eagle
</td>
<td>two strokes under par</td>
</tr>
<tr>
<td class="rowheader">-1</td>
<td>
Birdie
</td>
<td>one stroke under par</td>
</tr>
<tr>
<td class="rowheader">0</td>
<td>
Par
</td>
<td>strokes equal to par</td>
</tr>
<tr>
<td class="rowheader">+1</td>
<td>
Bogie
</td>
<td>one stroke more than par</td>
</tr>
<tr>
<td class="rowheader">+2</td>
<td>
Double Bogey (or Buzzard)
</td>
<td>two strokes over par</td>
</tr>
<tr>
<td class="rowheader">+3</td>
<td>Triple Bogey</td>
<td>three strokes over par</td>
</tr>
</tbody>
</table>
<p>The two basic forms of playing golf are match play and stroke play.</p>
<ul>
    <li>In match play, two players (or two teams) play each hole as a separate contest against each other. The party with the lower score wins that hole, or if the scores of both players or teams are equal the hole is "halved" (drawn). The game is won by the party that wins more holes than the other. In the case that one team or player has taken a lead that cannot be overcome in the number of holes remaining to be played, the match is deemed to be won by the party in the lead, and the remainder of the holes are not played. For example, if one party already has a lead of six holes, and only five holes remain to be played on the course, the match is over. At any given point, if the lead is equal to the number of holes remaining, the match is said to be "dormie", and is continued until the leader increases the lead by one hole, thereby winning the match, or until the match ends in a tie. When the game is tied after the predetermined number of holes have been played, it may be continued until one side takes a one-hole lead.</li>
    <li>In stroke play the score achieved for each and every hole of the round or tournament is added to produce the total score, and the player with the lowest score wins (Stroke play is the game most usually played by professional golfers).</li>
</ul>

<p>There are variations of these basic principles, including skins, stableford scoring, and team games including foursome and four-ball games.</p>


<script type="text/javascript">
AddLicenseInfo("Wikipedia", "http://en.wikipedia.org/wiki/Golf");
AddTagLine();
</script>



</body>
</html>
