<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US">
<head>
    <title>Playing Golf</title>
    <style type="text/css" media="screen">
		@import url( ../shared/style.css );
	</style>
	<script src="../shared/scormfunctions.js" type="text/javascript"></script>
	<script src="../shared/contentfunctions.js" type="text/javascript"></script>
</head>
<body>
<h1>Play of the game</h1>
<img src="playing.jpg" id="golfimg" alt="golfing image" />
<p>Every round of golf is based on playing a number of holes in a given order. A round typically consists of 18 holes that are played in the order determined by the course layout. On a nine-hole course, a standard round consists of two successive nine-hole rounds. Playing a hole on the golf course consists of hitting a ball from a tee on the teeing box (a marked area designated for the first shot of a hole, a tee shot), and once the ball comes to rest, striking it again. This process is repeated until the ball is in the cup. Once the ball is on the green (an area of finely cut grass) the ball is usually putted (hit along the ground) into the hole. The goal of resting the ball in the hole in as few strokes as possible may be impeded by hazards, such as bunkers and water hazards. In most typical forms of gameplay, each player plays his or her ball from the tee until it is holed.</p>

<p>Players can walk or drive in motorized carts over the course, either singly or in groups of two, three, or four, sometimes accompanied by caddies who carry and manage the players' equipment and give them advice.</p>

<p>In stroke play, the score consists of the number of strokes played plus any penalty strokes incurred. Penalty strokes are not actually strokes but penalty points that are added to the score for violations of rules or utilizing relief procedures.</p>

<script type="text/javascript">
AddLicenseInfo("Wikipedia", "http://en.wikipedia.org/wiki/Golf");
AddTagLine();
</script>
</body>
</html>
