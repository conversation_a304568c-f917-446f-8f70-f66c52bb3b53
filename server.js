const express = require('express');
const path = require('path');

const app = express();
const PORT = 8080;

// Import SCORM routes
const scormRoutes = require('./routes/scormRoutes');

// Middleware to parse JSON bodies
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/public', express.static(path.join(__dirname, 'public')));

// Use the SCORM routes
app.use(scormRoutes);

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
