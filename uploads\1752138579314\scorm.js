/* SCORM Implementation */
(function() {
    'use strict';

    var startTimeStamp, scormUnload, actions, student;

    function initializeSCORM() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.init();
        }
        return false;
    }

    function setSCORMValue(parameter, value) {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.set(parameter, value);
        }
        return false;
    }

    function getSCORMValue(parameter) {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.get(parameter);
        }
        return '';
    }

    function saveSCORMData() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.save();
        }
        return false;
    }

    function quitSCORM() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.quit();
        }
        return false;
    }

    // Initialize SCORM when page loads
    window.addEventListener('load', function() {
        initializeSCORM();
    });

    // Save data before page unloads
    window.addEventListener('beforeunload', function() {
        saveSCORMData();
        quitSCORM();
    });

})();