{"success": true, "data": {"files": [{"path": "imsmanifest.xml", "content": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<manifest identifier=\"finance_agm_learning_path\" version=\"1.0\"\n  xmlns=\"http://www.imsglobal.org/xsd/imscp_v1p1\"\n  xmlns:adlcp=\"http://www.adlnet.org/xsd/adlcp_v1p3\"\n  xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n  xsi:schemaLocation=\"http://www.imsglobal.org/xsd/imscp_v1p1 imscp_v1p1.xsd\n                      http://www.adlnet.org/xsd/adlcp_v1p3 adlcp_v1p3.xsd\">\n  <metadata>\n    <schema>ADL SCORM</schema>\n    <schemaversion>2004 4th Edition</schemaversion>\n    <adlcp:location>adlcp_rootv1p3.xsd</adlcp:location>\n    <lomimscc:lom>\n      <lomimscc:general>\n        <lomimscc:title>\n          <lomimscc:string language=\"en\">Learning Path for Associate General Manager in Finance</lomimscc:string>\n        </lomimscc:title>\n        <lomimscc:description>\n          <lomimscc:string language=\"en\"></lomimscc:string>\n        </lomimscc:description>\n      </lomimscc:general>\n    </lomimscc:lom>\n  </metadata>\n  <organizations default=\"finance_agm_learning_path_ORG\">\n    <organization identifier=\"finance_agm_learning_path_ORG\">\n      <title>Learning Path for Associate General Manager in Finance</title>\n      \n        <item identifier=\"MODULE_0\">\n          <title>Onboarding Journey</title>\n          \n            <item identifier=\"LESSON_0_0\" identifierref=\"RESOURCE_lesson-onboarding-1\">\n              <title>Introduction to Financial Management</title>\n            </item>\n          \n            <item identifier=\"LESSON_0_1\" identifierref=\"RESOURCE_lesson-onboarding-2\">\n              <title>Basics of Corporate Finance</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_1\">\n          <title>Financial Strategy Development</title>\n          \n            <item identifier=\"LESSON_1_0\" identifierref=\"RESOURCE_lesson-financial-strategy-1\">\n              <title>Building a Financial Strategy</title>\n            </item>\n          \n            <item identifier=\"LESSON_1_1\" identifierref=\"RESOURCE_lesson-financial-strategy-2\">\n              <title>Strategic Financial Planning</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_2\">\n          <title>Budgeting and Forecasting</title>\n          \n            <item identifier=\"LESSON_2_0\" identifierref=\"RESOURCE_lesson-budgeting-1\">\n              <title>Budgeting for Success</title>\n            </item>\n          \n            <item identifier=\"LESSON_2_1\" identifierref=\"RESOURCE_lesson-budgeting-2\">\n              <title>Understanding Financial Forecasting</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_3\">\n          <title>Risk Management in Finance</title>\n          \n            <item identifier=\"LESSON_3_0\" identifierref=\"RESOURCE_lesson-risk-management-1\">\n              <title>Introduction to Risk Management</title>\n            </item>\n          \n            <item identifier=\"LESSON_3_1\" identifierref=\"RESOURCE_lesson-risk-management-2\">\n              <title>Financial Risk Management Strategies</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_4\">\n          <title>Investment Decision Making</title>\n          \n            <item identifier=\"LESSON_4_0\" identifierref=\"RESOURCE_lesson-investment-1\">\n              <title>How to Make Investment Decisions</title>\n            </item>\n          \n            <item identifier=\"LESSON_4_1\" identifierref=\"RESOURCE_lesson-investment-2\">\n              <title>Evaluating Investment Opportunities</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_5\">\n          <title>Financial Analysis and Reporting</title>\n          \n            <item identifier=\"LESSON_5_0\" identifierref=\"RESOURCE_lesson-financial-analysis-1\">\n              <title>Financial Statement Analysis</title>\n            </item>\n          \n            <item identifier=\"LESSON_5_1\" identifierref=\"RESOURCE_lesson-financial-analysis-2\">\n              <title>Financial Reporting Techniques</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_6\">\n          <title>Corporate Financial Policies</title>\n          \n            <item identifier=\"LESSON_6_0\" identifierref=\"RESOURCE_lesson-corporate-policies-1\">\n              <title>Creating Corporate Financial Policies</title>\n            </item>\n          \n            <item identifier=\"LESSON_6_1\" identifierref=\"RESOURCE_lesson-corporate-policies-2\">\n              <title>Financial Policy Implementation</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_7\">\n          <title>Leadership and Management Skills</title>\n          \n            <item identifier=\"LESSON_7_0\" identifierref=\"RESOURCE_lesson-leadership-1\">\n              <title>Essential Leadership Skills for Managers</title>\n            </item>\n          \n            <item identifier=\"LESSON_7_1\" identifierref=\"RESOURCE_lesson-leadership-2\">\n              <title>Managing Financial Teams Effectively</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_8\">\n          <title>Ethics and Compliance in Finance</title>\n          \n            <item identifier=\"LESSON_8_0\" identifierref=\"RESOURCE_lesson-ethics-1\">\n              <title>Ethical Financial Management</title>\n            </item>\n          \n            <item identifier=\"LESSON_8_1\" identifierref=\"RESOURCE_lesson-ethics-2\">\n              <title>Maintaining Compliance in Finance</title>\n            </item>\n          \n        </item>\n      \n        <item identifier=\"MODULE_9\">\n          <title>Technology in Financial Services</title>\n          \n            <item identifier=\"LESSON_9_0\" identifierref=\"RESOURCE_lesson-technology-1\">\n              <title>Technology Innovations in Finance</title>\n            </item>\n          \n            <item identifier=\"LESSON_9_1\" identifierref=\"RESOURCE_lesson-technology-2\">\n              <title>Financial Technology Tools and Their Uses</title>\n            </item>\n          \n        </item>\n      \n    </organization>\n  </organizations>\n  <resources>\n    \n        <resource identifier=\"RESOURCE_lesson-onboarding-1\" type=\"webcontent\" href=\"lessons/lesson-onboarding-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-onboarding-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-onboarding-2\" type=\"webcontent\" href=\"lessons/lesson-onboarding-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-onboarding-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-financial-strategy-1\" type=\"webcontent\" href=\"lessons/lesson-financial-strategy-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-financial-strategy-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-financial-strategy-2\" type=\"webcontent\" href=\"lessons/lesson-financial-strategy-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-financial-strategy-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-budgeting-1\" type=\"webcontent\" href=\"lessons/lesson-budgeting-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-budgeting-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-budgeting-2\" type=\"webcontent\" href=\"lessons/lesson-budgeting-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-budgeting-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-risk-management-1\" type=\"webcontent\" href=\"lessons/lesson-risk-management-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-risk-management-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-risk-management-2\" type=\"webcontent\" href=\"lessons/lesson-risk-management-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-risk-management-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-investment-1\" type=\"webcontent\" href=\"lessons/lesson-investment-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-investment-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-investment-2\" type=\"webcontent\" href=\"lessons/lesson-investment-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-investment-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-financial-analysis-1\" type=\"webcontent\" href=\"lessons/lesson-financial-analysis-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-financial-analysis-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-financial-analysis-2\" type=\"webcontent\" href=\"lessons/lesson-financial-analysis-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-financial-analysis-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-corporate-policies-1\" type=\"webcontent\" href=\"lessons/lesson-corporate-policies-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-corporate-policies-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-corporate-policies-2\" type=\"webcontent\" href=\"lessons/lesson-corporate-policies-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-corporate-policies-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-leadership-1\" type=\"webcontent\" href=\"lessons/lesson-leadership-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-leadership-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-leadership-2\" type=\"webcontent\" href=\"lessons/lesson-leadership-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-leadership-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-ethics-1\" type=\"webcontent\" href=\"lessons/lesson-ethics-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-ethics-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-ethics-2\" type=\"webcontent\" href=\"lessons/lesson-ethics-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-ethics-2.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-technology-1\" type=\"webcontent\" href=\"lessons/lesson-technology-1.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-technology-1.html\"/>\n        </resource>\n      \n        <resource identifier=\"RESOURCE_lesson-technology-2\" type=\"webcontent\" href=\"lessons/lesson-technology-2.html\" adlcp:scormType=\"sco\">\n          <file href=\"lessons/lesson-technology-2.html\"/>\n        </resource>\n      \n    <resource identifier=\"SHARED_RESOURCES\" type=\"webcontent\">\n      <file href=\"css/styles.css\"/>\n      <file href=\"js/scorm_api.js\"/>\n      <file href=\"js/scripts.js\"/>\n    </resource>\n  </resources>\n</manifest>", "type": "text"}, {"path": "css/styles.css", "content": "/* Course styles */\nbody {\n  font-family: Arial, sans-serif;\n  line-height: 1.6;\n  margin: 0;\n  padding: 20px;\n}\n\n.lesson-container {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f9f9f9;\n  border-radius: 8px;\n}\n\n.video-container {\n  position: relative;\n  padding-bottom: 56.25%;\n  height: 0;\n  overflow: hidden;\n  margin: 20px 0;\n}\n\n.video-container iframe {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.quiz-options {\n  margin: 15px 0;\n}\n\n.quiz-options label {\n  display: block;\n  margin: 5px 0;\n}\n\nbutton {\n  padding: 10px 15px;\n  background: #4CAF50;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:hover {\n  background: #45a049;\n}", "type": "text"}, {"path": "js/scorm_api.js", "content": "// SCORM API Implementation\nconst SCORM = {\n  initialized: false,\n  \n  init: function() {\n    this.initialized = true;\n    console.log('SCORM initialized');\n    return true;\n  },\n  \n  set: function(param, value) {\n    if (!this.initialized) return false;\n    console.log('SCORM set:', param, value);\n    return true;\n  },\n  \n  get: function(param) {\n    if (!this.initialized) return '';\n    console.log('SCORM get:', param);\n    return '';\n  },\n  \n  save: function() {\n    if (!this.initialized) return false;\n    console.log('SCORM save');\n    return true;\n  },\n  \n  terminate: function() {\n    this.initialized = false;\n    console.log('SCORM terminated');\n    return true;\n  }\n};\n\n// Initialize on load\nwindow.addEventListener('load', () => SCORM.init());", "type": "text"}, {"path": "js/scripts.js", "content": "// Course scripts\nfunction markComplete() {\n  SCORM.set('cmi.completion_status', 'completed');\n  SCORM.save();\n  alert('Lesson marked as complete!');\n}\n\nfunction checkAnswer(questionId, correctAnswer) {\n  const selected = document.querySelector(`input[name=\"quiz_${questionId}\"]:checked`);\n  if (!selected) return alert('Please select an answer');\n  \n  const isCorrect = parseInt(selected.value) === correctAnswer;\n  alert(isCorrect ? 'Correct!' : 'Incorrect, please try again');\n  \n  if (isCorrect) {\n    SCORM.set(`cmi.interactions.${questionId}.result`, 'correct');\n    SCORM.save();\n  }\n}", "type": "text"}, {"path": "lessons/lesson-onboarding-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Introduction to Financial Management</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Introduction to Financial Management</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-onboarding-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Basics of Corporate Finance</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Basics of Corporate Finance</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-financial-strategy-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Building a Financial Strategy</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Building a Financial Strategy</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-financial-strategy-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Strategic Financial Planning</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Strategic Financial Planning</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-budgeting-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Budgeting for Success</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Budgeting for Success</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-budgeting-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Understanding Financial Forecasting</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Understanding Financial Forecasting</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-risk-management-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Introduction to Risk Management</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Introduction to Risk Management</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-risk-management-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Financial Risk Management Strategies</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Financial Risk Management Strategies</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-investment-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>How to Make Investment Decisions</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>How to Make Investment Decisions</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-investment-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Evaluating Investment Opportunities</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Evaluating Investment Opportunities</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-financial-analysis-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Financial Statement Analysis</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Financial Statement Analysis</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-financial-analysis-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Financial Reporting Techniques</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Financial Reporting Techniques</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-corporate-policies-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Creating Corporate Financial Policies</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Creating Corporate Financial Policies</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-corporate-policies-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Financial Policy Implementation</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Financial Policy Implementation</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-leadership-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Essential Leadership Skills for Managers</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Essential Leadership Skills for Managers</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-leadership-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Managing Financial Teams Effectively</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Managing Financial Teams Effectively</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-ethics-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Ethical Financial Management</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Ethical Financial Management</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-ethics-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Maintaining Compliance in Finance</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Maintaining Compliance in Finance</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-technology-1.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Technology Innovations in Finance</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Technology Innovations in Finance</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}, {"path": "lessons/lesson-technology-2.html", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Financial Technology Tools and Their Uses</title>\n  <link rel=\"stylesheet\" href=\"../css/styles.css\">\n</head>\n<body>\n  <div class=\"lesson-container\">\n    <h1>Financial Technology Tools and Their Uses</h1>\n    <div class=\"lesson-content\">\n      <div class=\"text-content\"></div>\n    </div>\n    <button class=\"complete-btn\" onclick=\"markComplete()\">Mark Complete</button>\n  </div>\n  <script src=\"../js/scorm_api.js\"></script>\n  <script src=\"../js/scripts.js\"></script>\n</body>\n</html>", "type": "text"}], "packageInfo": {"title": "Learning Path for Associate General Manager in Finance", "totalModules": 10, "totalLessons": 20, "estimatedSize": 25294}}}