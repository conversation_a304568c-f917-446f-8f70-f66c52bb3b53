<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Course Launch Page</title>
    
    
    <style type="text/css" media="screen">
		@import url( ../shared/jquery-ui.css );
	</style>
	
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.4.3/jquery.min.js" type="text/javascript"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.5/jquery-ui.min.js" type="text/javascript"></script>
    
    <script src="scormfunctions.js" type="text/javascript"></script>
    <script type="text/javascript">
    
    /*************************************/
    //functions for sizing the iFrame
    /*************************************/
    function setIframeHeight(id, navWidth) {
        if ( document.getElementById ) {
            var theIframe = document.getElementById(id);
            if (theIframe) {
                var height = getWindowHeight();
                theIframe.style.height = Math.round( height ) - navWidth + "px";
                theIframe.style.marginTop = Math.round( ((height - navWidth) - parseInt(theIframe.style.height) )/2 ) + "px";
            }
        }
    }

    function getWindowHeight() {
        var height = 0;
        if (window.innerHeight){
            height = window.innerHeight - 18;
        }
        else if (document.documentElement && document.documentElement.clientHeight){
            height = document.documentElement.clientHeight;
        }
        else if (document.body && document.body.clientHeight) {
            height = document.body.clientHeight;
        }
        return height;
    }
    
    function SetupIFrame(){
        //set our iFrame for the content to take up the full screen except for our navigation
        var navWidth = 40;
        setIframeHeight("contentFrame", navWidth);
        //need this in a setTimeout to avoid a timing error in IE6
        window.setTimeout('window.onresize = function() { setIframeHeight("contentFrame", ' + navWidth + '); }', 1000);
    }
    
    /*************************************/
    //content definition
    /*************************************/
    var queryString = new String(document.location.search);
    queryString = queryString.replace("?content=", "");
    queryString = queryString.toLowerCase();
    
    var hasAssessment = false;
    
    switch (queryString){
        case "playing":
            var pageArray = new Array(5);
            pageArray[0] = "Playing/Playing.html";
            pageArray[1] = "Playing/Par.html";
            pageArray[2] = "Playing/Scoring.html";
            pageArray[3] = "Playing/OtherScoring.html";
            pageArray[4] = "Playing/RulesOfGolf.html";
        break;
        case "etiquette":
            var pageArray = new Array(3);
            pageArray[0] = "Etiquette/Course.html";
            pageArray[1] = "Etiquette/Course.html";
            pageArray[2] = "Etiquette/Play.html";
        break;
        case "handicapping":
            var pageArray = new Array(4);
            pageArray[0] = "Handicapping/Overview.html";
            pageArray[1] = "Handicapping/CalculatingHandicap.html";
            pageArray[2] = "Handicapping/CalculatingScore.html";
            pageArray[3] = "Handicapping/CalculatingScore.html";
        break;
        case "havingfun":
            var pageArray = new Array(2);
            pageArray[0] = "HavingFun/HowToHaveFun.html";
            pageArray[1] = "HavingFun/MakeFriends.html";
        break;
        case "assessment":
            hasAssessment = true;
            var pageArray = new Array(1);
            pageArray[0] = "shared/assessmenttemplate.html?questions=Playing&questions=Etiquette&questions=Handicapping&questions=HavingFun";
        break;
    
    }
   
    
    
    /*************************************/
    //navigation functions
    /*************************************/
    
    var currentPage = null;
    var startTimeStamp = null;
    var processedUnload = false;
    var reachedEnd = false;
    
    function doStart(){
        
        
        $("#notesWin" ).dialog({ autoOpen: false, resizable: false, width: 600});
        
        //get the iFrame sized correctly and set up
        SetupIFrame();
        
        //record the time that the learner started the SCO so that we can report the total time
        startTimeStamp = new Date();
        
        //initialize communication with the LMS
        ScormProcessInitialize();
        
        //it's a best practice to set the completion status to incomplete when
        //first launching the course (if the course is not already completed)
        var completionStatus = ScormProcessGetValue("cmi.completion_status", true);
        if (completionStatus == "unknown"){
            ScormProcessSetValue("cmi.completion_status", "incomplete");
        }
        
        //see if the user stored a bookmark previously (don't check for errors
        //because cmi.location may not be initialized
        var bookmark = ScormProcessGetValue("cmi.location", false);
        
        //if there isn't a stored bookmark, start the user at the first page
        if (bookmark == ""){
            currentPage = 0;
        }
        else{
            //if there is a stored bookmark, prompt the user to resume from the previous location
            if (confirm("Would you like to resume from where you previously left off?")){
                currentPage = parseInt(bookmark, 10);
            }
            else{
                currentPage = 0;
            }
        }
        
        //don't enable Skip to Quiz if we're on the quiz
        if (hasAssessment == true){
            var jumpButton = document.getElementById("butJump");
            jumpButton.disabled = true;
        }
        
        goToPage();
    }
    
    function goToPage(){
    
        var theIframe = document.getElementById("contentFrame");
        var prevButton = document.getElementById("butPrevious");
        var nextButton = document.getElementById("butNext");
        
        //navigate the iFrame to the content
        theIframe.src = "../" + pageArray[currentPage];
        
        //disable the prev/next buttons if we are on the first or last page.
        if (currentPage == 0){
            nextButton.disabled = false;
            prevButton.disabled = true;
        }
        else if (currentPage == (pageArray.length - 1)){
            nextButton.disabled = true;
            prevButton.disabled = false;       
        }
        else{
            nextButton.disabled = false;
            prevButton.disabled = false;
        }
        
        if (pageArray.length == 1){
            nextButton.disabled = true;
            prevButton.disabled = true;
        }
        
        //save the current location as the bookmark
        ScormProcessSetValue("cmi.location", currentPage);
        
        //calculate and save the completion percentage based on current page and total number of pages
        var progress = 0
        if (currentPage > 0 && pageArray.length > 1){
            progress = currentPage / (pageArray.length - 1);
        }
        
        progress = Math.round(progress*100) / 100; //Round off to 2 decimal places
        ScormProcessSetValue("cmi.progress_measure", progress);
        
        //in this example, if there is an assessment, the SCO isn't completed until it is submitted
        if (currentPage == (pageArray.length - 1)){
            reachedEnd = true;
            
            //For simplicity's sake, mark the course as passed when it is completed
            //and doesn't have a test. This will make our sequencing based on global
            //objectives simpler.
            if (hasAssessment == false){
                ScormProcessSetValue("cmi.completion_status", "completed");
                
                //Now that we are using the 4th Edition features, we don't have to include this
                //extraneous satisfaction, everything can be based on completion.
                //ScormProcessSetValue("cmi.success_status", "passed");
            }
            
            //invoke Commit because sometimes that will trigger the sequencer to 
            //update the available navigational controls
            ScormProcessCommit();
        }
        
    }
    
    function doUnload(pressedExit){
        
        //don't call this function twice
        if (processedUnload == true){return;}
        
        processedUnload = true;
        
        //record the session time
        var endTimeStamp = new Date();
        var totalMilliseconds = (endTimeStamp.getTime() - startTimeStamp.getTime());
        var scormTime = ConvertMilliSecondsIntoSCORM2004Time(totalMilliseconds);
        
        ScormProcessSetValue("cmi.session_time", scormTime);
        
        //always default to saving the runtime data in this example
        ScormProcessSetValue("cmi.exit", "suspend");
        
        ScormProcessTerminate();
    }
    
    function doPrevious(){
        if (currentPage > 0){
            currentPage--;
        }
        goToPage();
    }
    
    function doNext(){
        if (currentPage < (pageArray.length - 1)){
            currentPage++;
        }
        goToPage();
    }
    
    function doJumpToTest(){
        ScormProcessSetValue("adl.nav.request", "{target=assessment_item}jump");
        doUnload(true);
    }
    
    function doShowNotes(){
        notesText = GetNotesText();
        $("#txtNotes").val(notesText);
        $("#notesWin" ).dialog('open');
    }
    
    function doSaveNotes(){
        SaveNotesText($("#txtNotes").val())
        $("#notesWin" ).dialog('close')
    }
    
    function GetNotesText(){
        dataBucketsCount = ScormProcessGetValue("adl.data._count")
        dataBucketsCount = parseInt(dataBucketsCount);
        
        for (var i=0; i < dataBucketsCount; i++){
            if (ScormProcessGetValue("adl.data." + i + ".id") == "com.scorm.golfsamples.sequencing.forcedsequential.notesStorage"){
                return ScormProcessGetValue("adl.data." + i + ".store")
            }
        }
        
        return "";
    }
    
    function SaveNotesText(text){
        dataBucketsCount = ScormProcessGetValue("adl.data._count")
        dataBucketsCount = parseInt(dataBucketsCount);
        
        for (var i=0; i < dataBucketsCount; i++){
            if (ScormProcessGetValue("adl.data." + i + ".id") == "com.scorm.golfsamples.sequencing.forcedsequential.notesStorage"){
                ScormProcessSetValue("adl.data." + i + ".store", text);
                return;
            }
        }
    }
    
    function doExit(){

        //note use of short-circuit AND. If the user reached the end, don't prompt.
        //just exit normally and submit the results.
        if (reachedEnd == false && confirm("Would you like to save your progress to resume later?")){
            //set exit to suspend
            ScormProcessSetValue("cmi.exit", "suspend");
            
            //issue a suspendAll navigation request
            ScormProcessSetValue("adl.nav.request", "suspendAll");
        }
        else{
            //set exit to normal
            ScormProcessSetValue("cmi.exit", "");
            
            //issue an exitAll navigation request
            ScormProcessSetValue("adl.nav.request", "exitAll");
        }
        
        //process the unload handler to close out the session.
        //the presense of an adl.nav.request will cause the LMS to 
        //take the content away from the user.
        doUnload(true);
        
    }
    
    //called from the assessmenttemplate.html page to record the results of a test
    //passes in score as a percentage
    function RecordTest(score){
        ScormProcessSetValue("cmi.score.raw", score);
        ScormProcessSetValue("cmi.score.min", "0");
        ScormProcessSetValue("cmi.score.max", "100");
        
        var scaledScore = score / 100;
        ScormProcessSetValue("cmi.score.scaled", scaledScore);
        
        ScormProcessSetValue("cmi.completion_status", "completed");
        
        //consider 70% to be passing
        if (score >= 70){
            ScormProcessSetValue("cmi.success_status", "passed");
        }
        else{
            ScormProcessSetValue("cmi.success_status", "failed");
        }
    }
    
    //SCORM requires time to be formatted in a specific way
    function ConvertMilliSecondsIntoSCORM2004Time(intTotalMilliseconds){
	
	    var ScormTime = "";
    	
	    var HundredthsOfASecond;	//decrementing counter - work at the hundreths of a second level because that is all the precision that is required
    	
	    var Seconds;	// 100 hundreths of a seconds
	    var Minutes;	// 60 seconds
	    var Hours;		// 60 minutes
	    var Days;		// 24 hours
	    var Months;		// assumed to be an "average" month (figures a leap year every 4 years) = ((365*4) + 1) / 48 days - 30.4375 days per month
	    var Years;		// assumed to be 12 "average" months
    	
	    var HUNDREDTHS_PER_SECOND = 100;
	    var HUNDREDTHS_PER_MINUTE = HUNDREDTHS_PER_SECOND * 60;
	    var HUNDREDTHS_PER_HOUR   = HUNDREDTHS_PER_MINUTE * 60;
	    var HUNDREDTHS_PER_DAY    = HUNDREDTHS_PER_HOUR * 24;
	    var HUNDREDTHS_PER_MONTH  = HUNDREDTHS_PER_DAY * (((365 * 4) + 1) / 48);
	    var HUNDREDTHS_PER_YEAR   = HUNDREDTHS_PER_MONTH * 12;
    	
	    HundredthsOfASecond = Math.floor(intTotalMilliseconds / 10);
    	
	    Years = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_YEAR);
	    HundredthsOfASecond -= (Years * HUNDREDTHS_PER_YEAR);
    	
	    Months = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_MONTH);
	    HundredthsOfASecond -= (Months * HUNDREDTHS_PER_MONTH);
    	
	    Days = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_DAY);
	    HundredthsOfASecond -= (Days * HUNDREDTHS_PER_DAY);
    	
	    Hours = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_HOUR);
	    HundredthsOfASecond -= (Hours * HUNDREDTHS_PER_HOUR);
    	
	    Minutes = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_MINUTE);
	    HundredthsOfASecond -= (Minutes * HUNDREDTHS_PER_MINUTE);
    	
	    Seconds = Math.floor(HundredthsOfASecond / HUNDREDTHS_PER_SECOND);
	    HundredthsOfASecond -= (Seconds * HUNDREDTHS_PER_SECOND);
    	
	    if (Years > 0) {
		    ScormTime += Years + "Y";
	    }
	    if (Months > 0){
		    ScormTime += Months + "M";
	    }
	    if (Days > 0){
		    ScormTime += Days + "D";
	    }
    	
	    //check to see if we have any time before adding the "T"
	    if ((HundredthsOfASecond + Seconds + Minutes + Hours) > 0 ){
    		
		    ScormTime += "T";
    		
		    if (Hours > 0){
			    ScormTime += Hours + "H";
		    }
    		
		    if (Minutes > 0){
			    ScormTime += Minutes + "M";
		    }
    		
		    if ((HundredthsOfASecond + Seconds) > 0){
			    ScormTime += Seconds;
    			
			    if (HundredthsOfASecond > 0){
				    ScormTime += "." + HundredthsOfASecond;
			    }
    			
			    ScormTime += "S";
		    }
    		
	    }
    	
	    if (ScormTime == ""){
		    ScormTime = "0S";
	    }
    	
	    ScormTime = "P" + ScormTime;
    	
	    return ScormTime;
    }

    </script>

</head>
<body onload="doStart(false);" onbeforeunload="doUnload(false);" onunload="doUnload();">
   
    <iframe width="100%" id="contentFrame" src=""></iframe>
    
    <div id="navDiv">
        <input type="button" value="<- Previous" id="butPrevious" onclick="doPrevious();"/>
        <input type="button" value="Next ->" id="butNext" onclick="doNext();"/>
        <!--<input type="button" value="Exit" id="butExit" onclick="doExit();"/>-->
        <input type="button" value="Skip to the Test" id="butJump" onclick="doJumpToTest();"/>
        <input type="button" value="Notes" id="butNotes" onclick="doShowNotes();"/>
    </div>
    
    <div id="notesWin" title="Golf Notes">
        <textarea id="txtNotes" rows="5" cols="50"></textarea>
        <input type="button" value="Save" id="butSaveNotes" onclick="doSaveNotes();"/>
    </div>
</body>
</html>
