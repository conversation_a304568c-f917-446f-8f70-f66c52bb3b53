<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	
	<xs:complexType name = "limitConditionsType">
		<xs:attribute name = "attemptLimit" type = "xs:nonNegativeInteger">
				<xs:annotation>
					<xs:documentation>Limit Condition Attempt Limit</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "attemptAbsoluteDurationLimit" type = "xs:duration" >
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Attempt Absolute Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "attemptExperiencedDurationLimit" type = "xs:duration">
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Attempt Experienced Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "activityAbsoluteDurationLimit" type = "xs:duration">
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Absolute Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "activityExperiencedDurationLimit" type = "xs:duration" >
				<xs:annotation>
					<xs:documentation>Limit Condition Activity Experienced Duration Limit.  Typed as xs:duration: see http://www.w3.org/TR/xmlschema-2/#duration</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "beginTimeLimit" type = "xs:dateTime">
				<xs:annotation>
					<xs:documentation>Limit Condition Begin Time Limit</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name = "endTimeLimit" type = "xs:dateTime">
				<xs:annotation>
					<xs:documentation>Limit Condition End Time Limit</xs:documentation>
				</xs:annotation>
			</xs:attribute>
		
	</xs:complexType>
</xs:schema>