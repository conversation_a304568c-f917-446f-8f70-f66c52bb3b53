<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MBA Course Journey</title>
    <style>
        
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8fafc;
      }

      #course-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .course-header {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
      }

      .course-header h1 {
        font-size: 2.5rem;
        color: #1a202c;
        margin-bottom: 15px;
      }

      .course-description {
        font-size: 1.1rem;
        color: #4a5568;
        max-width: 600px;
        margin: 0 auto;
      }

      .course-navigation {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
      }

      .nav-modules {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
      }

      .nav-module h3 {
        color: #2d3748;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e2e8f0;
      }

      .nav-lessons {
        list-style: none;
      }

      .nav-lesson {
        margin-bottom: 8px;
      }

      .nav-lesson a {
        color: #4299e1;
        text-decoration: none;
        padding: 8px 12px;
        display: block;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav-lesson a:hover {
        background: #ebf8ff;
      }

      .course-content {
        margin-bottom: 30px;
      }

      .module {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
      }

      .module-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
      }

      .module-header h2 {
        font-size: 1.8rem;
        margin-bottom: 10px;
      }

      .module-description {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 15px;
      }

      .module-meta {
        display: flex;
        gap: 20px;
        font-size: 0.9rem;
        opacity: 0.8;
      }

      .lesson {
        border-bottom: 1px solid #e2e8f0;
        padding: 30px;
      }

      .lesson:last-child {
        border-bottom: none;
      }

      .lesson-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .lesson-header h3 {
        color: #2d3748;
        font-size: 1.4rem;
      }

      .lesson-meta {
        display: flex;
        gap: 15px;
        font-size: 0.9rem;
        color: #718096;
      }

      .lesson-type {
        background: #e2e8f0;
        padding: 4px 8px;
        border-radius: 4px;
        text-transform: capitalize;
      }

      .video-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%;
        margin: 20px 0;
      }

      .lesson-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .lesson-content {
        margin-bottom: 25px;
      }

      .lesson-text {
        font-size: 1.1rem;
        line-height: 1.7;
        color: #4a5568;
      }

      .lesson-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
      }

      .btn-complete, .btn-next, .btn-finish {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s;
      }

      .btn-complete {
        background: #48bb78;
        color: white;
      }

      .btn-complete:hover {
        background: #38a169;
      }

      .btn-next, .btn-finish {
        background: #4299e1;
        color: white;
      }

      .btn-next:hover, .btn-finish:hover {
        background: #3182ce;
      }

      .course-progress {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: sticky;
        bottom: 20px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 10px;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #48bb78, #38a169);
        width: 0%;
        transition: width 0.3s ease;
      }

      .progress-text {
        font-weight: 500;
        color: #4a5568;
      }
    
    </style>
</head>
<body>
    <div id="course-container">
        <header class="course-header">
            <h1>MBA Course Journey</h1>
            <p class="course-description">AI-generated MBA Course Journey based on: mba course</p>
        </header>

        <nav class="course-navigation">
            <div class="nav-modules">
                
                    <div class="nav-module" data-module="0">
                        <h3>Introduction to MBA</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="0" data-lesson="0">
                                    <a href="#lesson_0_0">What is an MBA? Overview and Benefits</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="0" data-lesson="1">
                                    <a href="#lesson_0_1">Choosing the Right MBA Program</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="1">
                        <h3>Core MBA Courses</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="1" data-lesson="0">
                                    <a href="#lesson_1_0">Marketing Management Fundamentals</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="1" data-lesson="1">
                                    <a href="#lesson_1_1">Operations Management Basics</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="2">
                        <h3>Financial Management</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="2" data-lesson="0">
                                    <a href="#lesson_2_0">Introduction to Financial Management</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="2" data-lesson="1">
                                    <a href="#lesson_2_1">Corporate Finance Explained</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="3">
                        <h3>Strategic Management</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="3" data-lesson="0">
                                    <a href="#lesson_3_0">Understanding Strategic Management</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="3" data-lesson="1">
                                    <a href="#lesson_3_1">Competitive Strategy Essentials</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="4">
                        <h3>Leadership and Communication</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="4" data-lesson="0">
                                    <a href="#lesson_4_0">Effective Leadership Skills</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="4" data-lesson="1">
                                    <a href="#lesson_4_1">Improving Business Communication</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="5">
                        <h3>Human Resource Management</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="5" data-lesson="0">
                                    <a href="#lesson_5_0">HR Management Basics</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="5" data-lesson="1">
                                    <a href="#lesson_5_1">Workforce Planning and Employment</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="6">
                        <h3>Business Analytics</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="6" data-lesson="0">
                                    <a href="#lesson_6_0">Introduction to Business Analytics</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="6" data-lesson="1">
                                    <a href="#lesson_6_1">Data-Driven Decision Making</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="7">
                        <h3>Project Management</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="7" data-lesson="0">
                                    <a href="#lesson_7_0">Fundamentals of Project Management</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="7" data-lesson="1">
                                    <a href="#lesson_7_1">Agile Project Management</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="8">
                        <h3>Ethics and Corporate Responsibility</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="8" data-lesson="0">
                                    <a href="#lesson_8_0">Business Ethics in Corporate World</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="8" data-lesson="1">
                                    <a href="#lesson_8_1">Corporate Social Responsibility Explained</a>
                                </li>
                            
                        </ul>
                    </div>
                
                    <div class="nav-module" data-module="9">
                        <h3>Entrepreneurship</h3>
                        <ul class="nav-lessons">
                            
                                <li class="nav-lesson" data-module="9" data-lesson="0">
                                    <a href="#lesson_9_0">Entrepreneurship Essentials</a>
                                </li>
                            
                                <li class="nav-lesson" data-module="9" data-lesson="1">
                                    <a href="#lesson_9_1">Building a Startup from Scratch</a>
                                </li>
                            
                        </ul>
                    </div>
                
            </div>
        </nav>

        <main class="course-content">
            
      <section class="module" id="module_0" data-module="0">
        <div class="module-header">
          <h2>Introduction to MBA</h2>
          <p class="module-description">Learn about introduction to mba through curated video content</p>
          <div class="module-meta">
            <span class="duration">8 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_0_0" data-module="0" data-lesson="0">
              <div class="lesson-header">
                <h3>What is an MBA? Overview and Benefits</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">3:00</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/vrlutHYlIDg"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">What is an MBA? Overview and Benefits</h2><div class="content-video">
          <h3>🎥 What is an MBA? Overview and Benefits</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/vrlutHYlIDg"
              frameborder="0"
              allowfullscreen
              title="What is an MBA? Overview and Benefits"
              onload="trackVideoPlay('What is an MBA? Overview and Benefits')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 3:00</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 3:00 video covers key concepts in What is an MBA? Overview and Benefits. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(0, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(0, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_0_1" data-module="0" data-lesson="1">
              <div class="lesson-header">
                <h3>Choosing the Right MBA Program</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">4:29</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/a0JBvBVk3gA"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Choosing the Right MBA Program</h2><div class="content-video">
          <h3>🎥 Choosing the Right MBA Program</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/a0JBvBVk3gA"
              frameborder="0"
              allowfullscreen
              title="Choosing the Right MBA Program"
              onload="trackVideoPlay('Choosing the Right MBA Program')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 4:29</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 4:29 video covers key concepts in Choosing the Right MBA Program. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(0, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(1, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_1" data-module="1">
        <div class="module-header">
          <h2>Core MBA Courses</h2>
          <p class="module-description">Learn about core mba courses through curated video content</p>
          <div class="module-meta">
            <span class="duration">26 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_1_0" data-module="1" data-lesson="0">
              <div class="lesson-header">
                <h3>Marketing Management Fundamentals</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">13:54</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/65MQnEMf-uI"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Marketing Management Fundamentals</h2><div class="content-video">
          <h3>🎥 Marketing Management Fundamentals</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/65MQnEMf-uI"
              frameborder="0"
              allowfullscreen
              title="Marketing Management Fundamentals"
              onload="trackVideoPlay('Marketing Management Fundamentals')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 13:54</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 13:54 video covers key concepts in Marketing Management Fundamentals. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(1, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(1, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_1_1" data-module="1" data-lesson="1">
              <div class="lesson-header">
                <h3>Operations Management Basics</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">11:48</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/l-MnHBREzG8"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Operations Management Basics</h2><div class="content-video">
          <h3>🎥 Operations Management Basics</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/l-MnHBREzG8"
              frameborder="0"
              allowfullscreen
              title="Operations Management Basics"
              onload="trackVideoPlay('Operations Management Basics')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 11:48</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 11:48 video covers key concepts in Operations Management Basics. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(1, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(2, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_2" data-module="2">
        <div class="module-header">
          <h2>Financial Management</h2>
          <p class="module-description">Learn about financial management through curated video content</p>
          <div class="module-meta">
            <span class="duration">20 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_2_0" data-module="2" data-lesson="0">
              <div class="lesson-header">
                <h3>Introduction to Financial Management</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">12:04</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/MKLd1iw1lFw"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Introduction to Financial Management</h2><div class="content-video">
          <h3>🎥 Introduction to Financial Management</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/MKLd1iw1lFw"
              frameborder="0"
              allowfullscreen
              title="Introduction to Financial Management"
              onload="trackVideoPlay('Introduction to Financial Management')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 12:04</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 12:04 video covers key concepts in Introduction to Financial Management. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(2, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(2, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_2_1" data-module="2" data-lesson="1">
              <div class="lesson-header">
                <h3>Corporate Finance Explained</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">7:19</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/VYvOlggk0e4"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Corporate Finance Explained</h2><div class="content-video">
          <h3>🎥 Corporate Finance Explained</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/VYvOlggk0e4"
              frameborder="0"
              allowfullscreen
              title="Corporate Finance Explained"
              onload="trackVideoPlay('Corporate Finance Explained')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 7:19</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 7:19 video covers key concepts in Corporate Finance Explained. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(2, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(3, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_3" data-module="3">
        <div class="module-header">
          <h2>Strategic Management</h2>
          <p class="module-description">Learn about strategic management through curated video content</p>
          <div class="module-meta">
            <span class="duration">18 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_3_0" data-module="3" data-lesson="0">
              <div class="lesson-header">
                <h3>Understanding Strategic Management</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">8:26</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/5xD2JLleGqk"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Understanding Strategic Management</h2><div class="content-video">
          <h3>🎥 Understanding Strategic Management</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/5xD2JLleGqk"
              frameborder="0"
              allowfullscreen
              title="Understanding Strategic Management"
              onload="trackVideoPlay('Understanding Strategic Management')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 8:26</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 8:26 video covers key concepts in Understanding Strategic Management. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(3, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(3, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_3_1" data-module="3" data-lesson="1">
              <div class="lesson-header">
                <h3>Competitive Strategy Essentials</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">9:32</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/iuYlGRnC7J8"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Competitive Strategy Essentials</h2><div class="content-video">
          <h3>🎥 Competitive Strategy Essentials</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/iuYlGRnC7J8"
              frameborder="0"
              allowfullscreen
              title="Competitive Strategy Essentials"
              onload="trackVideoPlay('Competitive Strategy Essentials')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 9:32</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 9:32 video covers key concepts in Competitive Strategy Essentials. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(3, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(4, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_4" data-module="4">
        <div class="module-header">
          <h2>Leadership and Communication</h2>
          <p class="module-description">Learn about leadership and communication through curated video content</p>
          <div class="module-meta">
            <span class="duration">48 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_4_0" data-module="4" data-lesson="0">
              <div class="lesson-header">
                <h3>Effective Leadership Skills</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">8:56</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/bk4ERJ3MkCE"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Effective Leadership Skills</h2><div class="content-video">
          <h3>🎥 Effective Leadership Skills</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/bk4ERJ3MkCE"
              frameborder="0"
              allowfullscreen
              title="Effective Leadership Skills"
              onload="trackVideoPlay('Effective Leadership Skills')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 8:56</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 8:56 video covers key concepts in Effective Leadership Skills. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(4, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(4, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_4_1" data-module="4" data-lesson="1">
              <div class="lesson-header">
                <h3>Improving Business Communication</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">38:47</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/xu7hp9O54bk"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Improving Business Communication</h2><div class="content-video">
          <h3>🎥 Improving Business Communication</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/xu7hp9O54bk"
              frameborder="0"
              allowfullscreen
              title="Improving Business Communication"
              onload="trackVideoPlay('Improving Business Communication')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 38:47</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 38:47 video covers key concepts in Improving Business Communication. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(4, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(5, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_5" data-module="5">
        <div class="module-header">
          <h2>Human Resource Management</h2>
          <p class="module-description">Learn about human resource management through curated video content</p>
          <div class="module-meta">
            <span class="duration">19 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_5_0" data-module="5" data-lesson="0">
              <div class="lesson-header">
                <h3>HR Management Basics</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">10:57</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/bI9RZjF-538"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">HR Management Basics</h2><div class="content-video">
          <h3>🎥 HR Management Basics</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/bI9RZjF-538"
              frameborder="0"
              allowfullscreen
              title="HR Management Basics"
              onload="trackVideoPlay('HR Management Basics')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 10:57</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 10:57 video covers key concepts in HR Management Basics. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(5, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(5, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_5_1" data-module="5" data-lesson="1">
              <div class="lesson-header">
                <h3>Workforce Planning and Employment</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">7:32</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/nfaUfYEs56c"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Workforce Planning and Employment</h2><div class="content-video">
          <h3>🎥 Workforce Planning and Employment</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/nfaUfYEs56c"
              frameborder="0"
              allowfullscreen
              title="Workforce Planning and Employment"
              onload="trackVideoPlay('Workforce Planning and Employment')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 7:32</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 7:32 video covers key concepts in Workforce Planning and Employment. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(5, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(6, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_6" data-module="6">
        <div class="module-header">
          <h2>Business Analytics</h2>
          <p class="module-description">Learn about business analytics through curated video content</p>
          <div class="module-meta">
            <span class="duration">24 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_6_0" data-module="6" data-lesson="0">
              <div class="lesson-header">
                <h3>Introduction to Business Analytics</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">11:33</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/diaZdX1s5L4"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Introduction to Business Analytics</h2><div class="content-video">
          <h3>🎥 Introduction to Business Analytics</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/diaZdX1s5L4"
              frameborder="0"
              allowfullscreen
              title="Introduction to Business Analytics"
              onload="trackVideoPlay('Introduction to Business Analytics')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 11:33</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 11:33 video covers key concepts in Introduction to Business Analytics. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(6, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(6, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_6_1" data-module="6" data-lesson="1">
              <div class="lesson-header">
                <h3>Data-Driven Decision Making</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">12:27</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/tcImHmE6APc"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Data-Driven Decision Making</h2><div class="content-video">
          <h3>🎥 Data-Driven Decision Making</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/tcImHmE6APc"
              frameborder="0"
              allowfullscreen
              title="Data-Driven Decision Making"
              onload="trackVideoPlay('Data-Driven Decision Making')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 12:27</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 12:27 video covers key concepts in Data-Driven Decision Making. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(6, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(7, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_7" data-module="7">
        <div class="module-header">
          <h2>Project Management</h2>
          <p class="module-description">Learn about project management through curated video content</p>
          <div class="module-meta">
            <span class="duration">55 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_7_0" data-module="7" data-lesson="0">
              <div class="lesson-header">
                <h3>Fundamentals of Project Management</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">45:31</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/uGgBmamRkA4"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Fundamentals of Project Management</h2><div class="content-video">
          <h3>🎥 Fundamentals of Project Management</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/uGgBmamRkA4"
              frameborder="0"
              allowfullscreen
              title="Fundamentals of Project Management"
              onload="trackVideoPlay('Fundamentals of Project Management')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 45:31</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 45:31 video covers key concepts in Fundamentals of Project Management. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(7, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(7, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_7_1" data-module="7" data-lesson="1">
              <div class="lesson-header">
                <h3>Agile Project Management</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">9:18</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/thsFsPnUHRA"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Agile Project Management</h2><div class="content-video">
          <h3>🎥 Agile Project Management</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/thsFsPnUHRA"
              frameborder="0"
              allowfullscreen
              title="Agile Project Management"
              onload="trackVideoPlay('Agile Project Management')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 9:18</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 9:18 video covers key concepts in Agile Project Management. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(7, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(8, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_8" data-module="8">
        <div class="module-header">
          <h2>Ethics and Corporate Responsibility</h2>
          <p class="module-description">Learn about ethics and corporate responsibility through curated video content</p>
          <div class="module-meta">
            <span class="duration">6 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_8_0" data-module="8" data-lesson="0">
              <div class="lesson-header">
                <h3>Business Ethics in Corporate World</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">2:12</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/b_n6i1ug0tQ"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Business Ethics in Corporate World</h2><div class="content-video">
          <h3>🎥 Business Ethics in Corporate World</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/b_n6i1ug0tQ"
              frameborder="0"
              allowfullscreen
              title="Business Ethics in Corporate World"
              onload="trackVideoPlay('Business Ethics in Corporate World')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 2:12</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 2:12 video covers key concepts in Business Ethics in Corporate World. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(8, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(8, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_8_1" data-module="8" data-lesson="1">
              <div class="lesson-header">
                <h3>Corporate Social Responsibility Explained</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">2:55</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/ZoKihFLCY0s"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Corporate Social Responsibility Explained</h2><div class="content-video">
          <h3>🎥 Corporate Social Responsibility Explained</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/ZoKihFLCY0s"
              frameborder="0"
              allowfullscreen
              title="Corporate Social Responsibility Explained"
              onload="trackVideoPlay('Corporate Social Responsibility Explained')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 2:55</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 2:55 video covers key concepts in Corporate Social Responsibility Explained. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(8, 1)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(9, 0)">Next Module</button>
              </div>
            </article>
          
        </div>
      </section>
    
      <section class="module" id="module_9" data-module="9">
        <div class="module-header">
          <h2>Entrepreneurship</h2>
          <p class="module-description">Learn about entrepreneurship through curated video content</p>
          <div class="module-meta">
            <span class="duration">23 min</span>
            <span class="lesson-count">2 lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          
            <article class="lesson" id="lesson_9_0" data-module="9" data-lesson="0">
              <div class="lesson-header">
                <h3>Entrepreneurship Essentials</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">15:20</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/eHJnEHyyN1Y"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Entrepreneurship Essentials</h2><div class="content-video">
          <h3>🎥 Entrepreneurship Essentials</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/eHJnEHyyN1Y"
              frameborder="0"
              allowfullscreen
              title="Entrepreneurship Essentials"
              onload="trackVideoPlay('Entrepreneurship Essentials')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 15:20</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 15:20 video covers key concepts in Entrepreneurship Essentials. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(9, 0)">
                  Mark Complete
                </button>
                <button class="btn-next" onclick="goToLesson(9, 1)">Next Lesson</button>
              </div>
            </article>
          
            <article class="lesson" id="lesson_9_1" data-module="9" data-lesson="1">
              <div class="lesson-header">
                <h3>Building a Startup from Scratch</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">video</span>
                  <span class="lesson-duration">6:41</span>
                </div>
              </div>

              <div class="lesson-content">
                
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/bNpx7gpSqbY"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        <h2 class="content-heading">Building a Startup from Scratch</h2><div class="content-video">
          <h3>🎥 Building a Startup from Scratch</h3>
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/bNpx7gpSqbY"
              frameborder="0"
              allowfullscreen
              title="Building a Startup from Scratch"
              onload="trackVideoPlay('Building a Startup from Scratch')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: 6:41</p>
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div><div class="content-text"><p>This 6:41 video covers key concepts in Building a Startup from Scratch. Take notes and pause as needed to fully understand the material.</p></div>
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(9, 1)">
                  Mark Complete
                </button>
                <button class="btn-finish" onclick="finishCourse()">Finish Course</button>
              </div>
            </article>
          
        </div>
      </section>
    
        </main>

        <div class="course-progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <span class="progress-text" id="progress-text">0% Complete</span>
        </div>
    </div>

    <script src="pipwerksWrapper.js"></script>
    <script src="scorm.js"></script>
    <script>
        
      let completedLessons = new Set();
      let currentLesson = { module: 0, lesson: 0 };
      const totalLessons = 20;

      // Initialize SCORM
      function initializeSCORM() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          pipwerks.SCORM.init();

          // Get existing progress
          const suspendData = pipwerks.SCORM.get('cmi.suspend_data');
          if (suspendData) {
            try {
              const progress = JSON.parse(suspendData);
              completedLessons = new Set(progress.completed || []);
              currentLesson = progress.current || { module: 0, lesson: 0 };
            } catch (e) {
              console.warn('Could not parse suspend data:', e);
            }
          }

          updateProgress();
          goToLesson(currentLesson.module, currentLesson.lesson);
        }
      }

      function markLessonComplete(moduleIndex, lessonIndex) {
        const lessonId = moduleIndex + '_' + lessonIndex;
        completedLessons.add(lessonId);

        // Update SCORM
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          const progress = {
            completed: Array.from(completedLessons),
            current: currentLesson
          };
          pipwerks.SCORM.set('cmi.suspend_data', JSON.stringify(progress));
          pipwerks.SCORM.set('cmi.completion_status', 'incomplete');

          if (completedLessons.size === totalLessons) {
            pipwerks.SCORM.set('cmi.completion_status', 'completed');
            pipwerks.SCORM.set('cmi.success_status', 'passed');
          }

          pipwerks.SCORM.save();
        }

        updateProgress();

        // Visual feedback
        const lessonElement = document.getElementById('lesson_' + moduleIndex + '_' + lessonIndex);
        if (lessonElement) {
          lessonElement.style.opacity = '0.7';
          lessonElement.style.background = '#f0fff4';
        }
      }

      function goToLesson(moduleIndex, lessonIndex) {
        currentLesson = { module: moduleIndex, lesson: lessonIndex };

        // Hide all lessons
        document.querySelectorAll('.lesson').forEach(lesson => {
          lesson.style.display = 'none';
        });

        // Show target lesson
        const targetLesson = document.getElementById('lesson_' + moduleIndex + '_' + lessonIndex);
        if (targetLesson) {
          targetLesson.style.display = 'block';
          targetLesson.scrollIntoView({ behavior: 'smooth' });
        }

        // Update navigation
        document.querySelectorAll('.nav-lesson a').forEach(link => {
          link.classList.remove('active');
        });

        const activeLink = document.querySelector('[href="#lesson_' + moduleIndex + '_' + lessonIndex + '"]');
        if (activeLink) {
          activeLink.classList.add('active');
        }
      }

      function updateProgress() {
        const progressPercent = Math.round((completedLessons.size / totalLessons) * 100);

        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressFill) {
          progressFill.style.width = progressPercent + '%';
        }

        if (progressText) {
          progressText.textContent = progressPercent + '% Complete (' + completedLessons.size + '/' + totalLessons + ')';
        }
      }

      function finishCourse() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          pipwerks.SCORM.set('cmi.completion_status', 'completed');
          pipwerks.SCORM.set('cmi.success_status', 'passed');
          pipwerks.SCORM.save();
        }

        alert('Congratulations! You have completed the course.');
      }

      // Initialize when page loads
      document.addEventListener('DOMContentLoaded', function() {
        initializeSCORM();

        // Show first lesson by default
        goToLesson(0, 0);
      });

      // Handle page unload
      window.addEventListener('beforeunload', function() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          const progress = {
            completed: Array.from(completedLessons),
            current: currentLesson
          };
          pipwerks.SCORM.set('cmi.suspend_data', JSON.stringify(progress));
          pipwerks.SCORM.save();
          pipwerks.SCORM.quit();
        }
      });
    
    </script>
</body>
</html>