<!DOCTYPE html>
<html>
<head>
    <title>Test SCORM Package</title>
</head>
<body>
    <h1>Test SCORM Content</h1>
    <p>This is a test SCORM package for debugging purposes.</p>
    
    <script>
        // Basic SCORM API detection
        if (window.parent && window.parent.API) {
            console.log('SCORM 1.2 API detected');
            var api = window.parent.API;
            api.LMSInitialize('');
            api.LMSSetValue('cmi.core.lesson_status', 'completed');
            api.LMSCommit('');
        } else if (window.parent && window.parent.API_1484_11) {
            console.log('SCORM 2004 API detected');
            var api = window.parent.API_1484_11;
            api.Initialize('');
            api.SetValue('cmi.completion_status', 'completed');
            api.Commit('');
        } else {
            console.log('No SCORM API detected');
        }
    </script>
</body>
</html>
