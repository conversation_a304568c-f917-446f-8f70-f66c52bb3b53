<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0util.xsd"/>
	<xs:complexType name = "objectiveType">
		<xs:sequence>
			<xs:element name = "minNormalizedMeasure" type = "measureType"
				 block = "#all" default = "1.00000" minOccurs = "0"/>
			<xs:element name = "mapInfo"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded">
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base = "objectiveMappingType"/>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name = "satisfiedByMeasure" default = "false" type = "xs:boolean"/>
	</xs:complexType>
	<xs:complexType name = "objectivesType">
		<xs:annotation>
			<xs:documentation>The specification states: "Each activity must have one and only one objective that contributes to rollup".  The following type describes an unbounded set of elements all named "objective" that do not contribute to rollup, and one element called "primaryObjective" that contributes to rollup.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name = "primaryObjective"
				 block = "#all">
				<xs:annotation>
					<xs:documentation>Contributes to rollup of the activity. </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base = "objectiveType">
							<xs:attribute name = "objectiveID" type = "xs:anyURI"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name = "objective"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded">
				<xs:annotation>
					<xs:documentation>Does not contribute to the rollup of the activity.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base = "objectiveType">
							<xs:attribute name = "objectiveID" use = "required" type = "xs:anyURI"/>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name = "objectiveMappingType">
		<xs:annotation>
			<xs:documentation>The type that describes an individual objective mapping.  Mapping one local objective GUID to one global objective GUID</xs:documentation>
		</xs:annotation>
		<xs:attribute name = "targetObjectiveID" use = "required" type = "xs:anyURI"/>
		<xs:attribute name = "readSatisfiedStatus" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "readNormalizedMeasure" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "writeSatisfiedStatus" default = "false" type = "xs:boolean"/>
		<xs:attribute name = "writeNormalizedMeasure" default = "false" type = "xs:boolean"/>
	</xs:complexType>
</xs:schema>