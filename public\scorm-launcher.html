<html>
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCORM Integration</title>
    <script src="scorm-again.min.js"></script>
    <style>
        /* CSS styling layout design */
body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    height: 100vh;
    width: 100vw;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.iframe {
    width: 100%;
    height: 100%;
    border: none;
}
.message {
    color: #333;
    text-align: center;
    font-size: 1.2em;
    display: none;
}

#main {
    position: relative;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}
        </style>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        loadScormCourse();
      });

      function loadScormCourse() {
        const mainContainer = document.getElementById("main");
        const loader = document.getElementById("loader");
        const message = document.querySelector(".message");

        // Show loader while course is loading
        loader.style.display = "block";
      }

      function getQueryParameter(param)
      {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
      }
      const launchUrl =getQueryParameter('launchUrl');
      if(!launchUrl) {
        showMessage("Error: Missing SCORM course URL.")
        return;
      }

      const userId="123";
      const version="1.2";
      const studentName="codeSnippetByRohan"
      const apiUrl ="http://localhost:8080"

      const apiSettings={
        lmsCommitUrl:`${apiUrl]/${userId}/set-log`,
        logLevel:1,
        autocommitSeconds:5,
        autocommit:true,
        asycnCommit:true,
        xhrWithCredentials:false,
        xhrHeaders:{
          accept:"application/json",
        },
            responseHandler:res =>{
            if(res.status === 406)
            {
            alert("please login.");
            window.close();

            }
            }
      };

      fetch(`${apiUrl}/${UserId}/get-log`,{
      method:'GET',
      headers:apiSettings{
      accept:'application/json',
      }).then(response =>{
      if(response.status === 200){
      return response.json();
      {
      else
      {
      throw new Error ()"Failed to retrieve data");
      }).then(result=>{
      initializeScromAPI(
      version,
      userId,
      staudentName,
      apiSettings,
      result,);
    // Append iframe and load course
const iframe = document.createElement("iframe");
iframe.src = launchUrl;
mainContainer.appendChild(iframe);
iframe.style.display = "block";
}).catch(error => {
    showMessage(`Error: ${error.message}`);
}).finally(() => {
    loader.style.display = "none";
});
     

    function initializeScormAPI(version, userId, studentName, apiSettings, result) {
    if (version === '1.2') {
        window.API = new window.Scorm12API(apiSettings);
        window.API.cmi.core.student_id = userId;
        window.API.cmi.core.student_name = studentName;
    } else if (version === '2004') {
        window.API_1484_11 = new window.Scorm2004API(apiSettings);
        window.API_1484_11.cmi.learner_id = userId;
        window.API_1484_11.cmi.learner_name = studentName;
    }

    if (result && result.cmi) {
        const cmiData = result.cmi;
        if (version === '1.2') {
            window.API.loadFromJSON(cmiData);
        } else if (version === '2004') {
            window.API_1484_11.loadFromJSON(cmiData);
        }
    }
}



    function showMessage(msg)
    {
        const message =document.querySelector(".messaage");
        message.innerText = msg;
        message.style.display = "block";
    }

    </script>
    </head>
    <body>
      <div id="main">
        <div id="loader">
          <p>Loading...</p>
        </div>
        <div class="message">
          <p>Course loaded successfully!</p>
        </div>
      </div>
    </body>
