<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" 
           xmlns="http://www.imsglobal.org/xsd/imscp_v1p1"
           targetNamespace="http://www.imsglobal.org/xsd/imscp_v1p1"
           version="SCORM 2004 4th Edition"
           elementFormDefault="qualified">

  <xs:element name="manifest" type="manifestType"/>

  <xs:complexType name="manifestType">
    <xs:sequence>
      <xs:element name="metadata" type="metadataType" minOccurs="0"/>
      <xs:element name="organizations" type="organizationsType"/>
      <xs:element name="resources" type="resourcesType"/>
      <xs:element name="manifest" type="manifestType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="identifier" type="xs:ID" use="required"/>
    <xs:attribute name="version" type="xs:string" use="optional"/>
    <xs:attribute ref="xml:base" use="optional"/>
  </xs:complexType>

  <xs:complexType name="metadataType">
    <xs:sequence>
      <xs:element name="schema" type="xs:string" minOccurs="0"/>
      <xs:element name="schemaversion" type="xs:string" minOccurs="0"/>
      <xs:any namespace="##other" processContents="strict" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="organizationsType">
    <xs:sequence>
      <xs:element name="organization" type="organizationType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="default" type="xs:IDREF" use="optional"/>
  </xs:complexType>

  <xs:complexType name="organizationType">
    <xs:sequence>
      <xs:element name="title" type="xs:string"/>
      <xs:element name="item" type="itemType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="metadata" type="metadataType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="identifier" type="xs:ID" use="required"/>
  </xs:complexType>

  <xs:complexType name="itemType">
    <xs:sequence>
      <xs:element name="title" type="xs:string"/>
      <xs:element name="item" type="itemType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="metadata" type="metadataType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="identifier" type="xs:ID" use="required"/>
    <xs:attribute name="identifierref" type="xs:string" use="optional"/>
    <xs:attribute name="isvisible" type="xs:boolean" use="optional" default="true"/>
    <xs:attribute name="parameters" type="xs:string" use="optional"/>
  </xs:complexType>

  <xs:complexType name="resourcesType">
    <xs:sequence>
      <xs:element name="resource" type="resourceType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="resourceType">
    <xs:sequence>
      <xs:element name="metadata" type="metadataType" minOccurs="0"/>
      <xs:element name="file" type="fileType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="identifier" type="xs:ID" use="required"/>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="href" type="xs:anyURI" use="optional"/>
    <xs:attribute ref="xml:base" use="optional"/>
  </xs:complexType>

  <xs:complexType name="fileType">
    <xs:attribute name="href" type="xs:anyURI" use="required"/>
  </xs:complexType>

  <xs:attribute name="base" type="xs:anyURI"/>
</xs:schema> 