const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const http = require('http');

function testUpload() {
    const form = new FormData();
    const filePath = path.join(__dirname, 'test-scorm.zip');
    
    if (!fs.existsSync(filePath)) {
        console.error('Test file not found:', filePath);
        return;
    }
    
    form.append('scormPackage', fs.createReadStream(filePath));
    
    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/upload',
        method: 'POST',
        headers: form.getHeaders()
    };
    
    const req = http.request(options, (res) => {
        console.log('Status:', res.statusCode);
        console.log('Headers:', res.headers);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Response:', data);
        });
    });
    
    req.on('error', (err) => {
        console.error('Request error:', err);
    });
    
    form.pipe(req);
}

testUpload();
