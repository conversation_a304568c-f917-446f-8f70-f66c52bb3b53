<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	
	<xs:complexType name = "controlModeType">
		<xs:annotation>
			<xs:documentation>The type associated with a control-mode element (see the element controlMode)</xs:documentation>
		</xs:annotation>
		<xs:attribute name = "choice" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "choiceExit" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "flow" default = "false" type = "xs:boolean"/>
		<xs:attribute name = "forwardOnly" default = "false" type = "xs:boolean"/>
		<xs:attribute name = "useCurrentAttemptObjectiveInfo" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "useCurrentAttemptProgressInfo" default = "true" type = "xs:boolean"/>
	</xs:complexType>
</xs:schema>