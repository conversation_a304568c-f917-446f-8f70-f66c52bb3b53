<!DOCTYPE html>
<html>
<head>
    <title>Test Upload</title>
</head>
<body>
    <h1>Test SCORM Upload</h1>
    <form action="http://localhost:8080/upload" method="POST" enctype="multipart/form-data">
        <input type="file" name="scormPackage" accept=".zip" required>
        <button type="submit">Upload Test</button>
    </form>
    
    <script>
        // Add some debugging
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('Form submitted');
            const fileInput = document.querySelector('input[type="file"]');
            if (fileInput.files.length > 0) {
                console.log('File selected:', fileInput.files[0].name);
            } else {
                console.log('No file selected');
            }
        });
    </script>
</body>
</html>
