<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0util.xsd"/>
	<xs:complexType name = "exitConditionRuleType">
		<xs:complexContent>
			<xs:extension base = "sequencingRuleType">
				<xs:sequence>
					<xs:element name = "ruleAction"
						 block = "#all">
						<xs:complexType>
							<xs:attribute name = "action" use = "required" type = "exitConditionRuleActionType"/>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name = "postConditionRuleType">
		<xs:annotation>
			<xs:documentation>postConditionSequencingRuleType is derived by extension from sequencingRuleType.  It adds an element ruleAction that is a simpleType constrained to a vocabulary relevant to post-Condition sequencing rules</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base = "sequencingRuleType">
				<xs:sequence>
					<xs:element name = "ruleAction"
						 block = "#all">
						<xs:complexType>
							<xs:attribute name = "action" use = "required" type = "postConditionRuleActionType"/>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name = "preConditionRuleType">
		<xs:complexContent>
			<xs:extension base = "sequencingRuleType">
				<xs:sequence>
					<xs:element name = "ruleAction"
						 block = "#all">
						<xs:complexType>
							<xs:attribute name = "action" use = "required" type = "preConditionRuleActionType"/>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name = "sequencingRuleType" abstract = "true">
		<xs:sequence>
			<xs:element name = "ruleConditions"
				 block = "#all" minOccurs = "0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name = "ruleCondition"
							 block = "#all" maxOccurs = "unbounded">
							<xs:complexType>
								<xs:attribute name = "referencedObjective" type = "xs:anyURI"/>
								<xs:attribute name = "measureThreshold" type = "measureType"/>
								<xs:attribute name = "operator" default = "noOp" type = "conditionOperatorType"/>
								<xs:attribute name = "condition" use = "required" type = "sequencingRuleConditionType"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name = "conditionCombination" default = "all" type = "conditionCombinationType"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name = "sequencingRulesType">
		<xs:sequence>
			<xs:element name = "preConditionRule" type = "preConditionRuleType"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded"/>
			<xs:element name = "exitConditionRule" type = "exitConditionRuleType"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded"/>
			<xs:element name = "postConditionRule" type = "postConditionRuleType"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name = "exitConditionRuleActionType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "exit"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "postConditionRuleActionType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "exitParent"/>
			<xs:enumeration value = "exitAll"/>
			<xs:enumeration value = "retry"/>
			<xs:enumeration value = "retryAll"/>
			<xs:enumeration value = "continue"/>
			<xs:enumeration value = "previous"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name = "preConditionRuleActionType">
		<xs:restriction base = "xs:token">
			<xs:enumeration value = "skip"/>
			<xs:enumeration value = "disabled"/>
			<xs:enumeration value = "hiddenFromChoice"/>
			<xs:enumeration value = "stopForwardTraversal"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>