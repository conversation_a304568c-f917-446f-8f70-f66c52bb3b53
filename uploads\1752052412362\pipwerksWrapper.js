/* pipwerks SCORM Wrapper for JavaScript */
var pipwerks = {};
pipwerks.UTILS = {};
pipwerks.debug = { isActive: false };

pipwerks.SCORM = {
    version: null,
    handleCompletionStatus: true,
    handleExitMode: true,
    API: { handle: null, isFound: false },
    connection: { isActive: false },
    data: { completionStatus: null, exitStatus: null },

    init: function() {
        console.log('SCORM initialized');
        this.connection.isActive = true;
        return true;
    },

    get: function(parameter) {
        console.log('SCORM get:', parameter);
        return '';
    },

    set: function(parameter, value) {
        console.log('SCORM set:', parameter, value);
        return true;
    },

    save: function() {
        console.log('SCORM save');
        return true;
    },

    quit: function() {
        console.log('SCORM quit');
        this.connection.isActive = false;
        return true;
    }
};