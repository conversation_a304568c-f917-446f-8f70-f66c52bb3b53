const express = require('express');
const multer = require('multer');
const path = require('path');
const unzipper = require('unzipper');
const fs = require('fs'); // File system module
const router = express.Router();

// Multer storage setup for SCORM uploads
const storage = multer.diskStorage({
  destination: './uploads',
  filename: function(req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});
// POST route to set SCORM log data
router.post('/:userId/set-log', (req, res) => {
    const { userId } = req.params;
    const { cmi } = req.body; // Assuming cmi data comes in request body

    // Update or add log for the given userId
    scormLogs[userId] = {
        lessonStatus: cmi.core.lesson_status,
        score: cmi.core.score, // Fixed typo (core_score -> core.score)
        sessionTime: cmi.core.session_time,
        timestamp: new Date().toISOString(),
        cmi,
    };

    // Write updated logs back to file
    writeLogsToFile(scormLogs);

    console.log('Log saved for user', userId);
    res.json({ 
        message: 'Log saved successfully', 
        logs: scormLogs[userId]
    });
});

// GET route to retrieve SCORM log data
router.get('/:userId/get-log', (req, res) => {
    const { userId } = req.params;
    
    if (!scormLogs[userId]) {
        return res.status(404).json({ message: 'User logs not found' });
    }

    res.json({ logs: scormLogs[userId] });
});

const upload = multer({
  storage: storage,
  // 100MB file size limit
  limits: { fileSize: 100000000 }
}).single('scormPackage');

// Route to handle SCORM package uploads and extra...
router.post('/upload', (req, res) => {
  upload(req, res, (err) => {
    if (err) {
      return res.status(500).send('File upload failed.');
    }

    const uploadedFilePath = path.join(__dirname, '../uploads', req.file.filename);
    const extractDir = path.join(__dirname, '../uploads', Date.now().toString());

    // Extract the SCORM package
    fs.createReadStream(uploadedFilePath).pipe(unzipper.Extract({
      path: extractDir
    })).on('close', () => {
      // Provide the link to the SCORM Package launch file
      const launchFileUrl = `/uploads/${path.basename(extractDir)}/index.html`;

      // Redirect to the HTML page in the public folder with the launchFileUrl as a query
      res.redirect('/public/scorm-launcher.html?launchUrl=${encodeURIComponent(launchFileUrl)}');
      // res.redirect(`/public/scorm-launcher.html?launchUrl=${encodeURIComponent(launchFileUrl)}`);

      res.status(200).send("File Uploaded");
    });
  });
});


module.exports = router;
