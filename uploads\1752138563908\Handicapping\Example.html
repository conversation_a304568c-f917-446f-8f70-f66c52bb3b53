<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US">
<head>
    <title>Handicapping Example</title>
    <style type="text/css" media="screen">
		@import url( ../shared/style.css );
	</style>
	<script src="../shared/scormfunctions.js" type="text/javascript"></script>
	<script src="../shared/contentfunctions.js" type="text/javascript"></script>
</head>
<body>
    <h1>Handicaping Example</h1>
    <img src="example.jpg" id="golfimg" alt="golfing image"/>
<p>Let's say that we have four golfers: <PERSON>, <PERSON>, <PERSON>, and <PERSON>, of various abilities who are in a competition against each other. Here are the players and their handicaps:</p>
<table border="1" cellpadding="3" cellspacing="0">
<tbody><tr>
<td>Alex</td>
<td>14.8</td>
</tr>

<tr>
<td>Bob</td>
<td>9.9</td>
</tr>
<tr>
<td>Chris</td>
<td>1.5</td>
</tr>
<tr>
<td>Dan</td>
<td>26.4</td>

</tr>
</tbody></table>
<p>The course has the following slope and rating:</p>
<ul>
<li>Rating: 70.9</li>
<li>Slope: 120</li>
</ul>
<p>So, using the formulas above, here are their <i>course handicaps</i> (only the slope is used to determine the course handicap):</p>
<table border="1" cellpadding="3" cellspacing="0">
<tbody><tr>

<td>Alex</td>
<td>16</td>
</tr>
<tr>
<td>Bob</td>
<td>11</td>
</tr>
<tr>
<td>Chris</td>
<td>2</td>
</tr>

<tr>
<td>Dan</td>
<td>28</td>
</tr>
</tbody></table>
<p><br/>
And, finally, here are their gross and their net scores:</p>
<table border="1" cellpadding="3" cellspacing="0">
<tbody><tr>
<td><b>Golfer</b></td>
<td><b>Gross</b></td>
<td><b>Net</b></td>

</tr>
<tr>
<td>Alex</td>
<td>91</td>
<td>75</td>
</tr>
<tr>
<td>Bob</td>
<td>86</td>
<td>75</td>
</tr>

<tr>
<td>Chris</td>
<td>74</td>
<td>72</td>
</tr>
<tr>
<td>Dan</td>
<td>99</td>
<td>71</td>
</tr>
</tbody></table>

<p>Dan wins. He is the only one in the group who actually shot better than his handicap, so he deserved to win.</p>

<script type="text/javascript">
AddLicenseInfo("Wikipedia", "http://en.wikipedia.org/wiki/Golf_handicap");
AddTagLine();
</script>
</body>
</html>
