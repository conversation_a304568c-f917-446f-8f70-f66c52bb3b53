<?xml version = "1.0" encoding = "UTF-8"?>
<!--Generated by Turbo XML 2.3.1.100. Conforms to w3c http://www.w3.org/2001/XMLSchema-->
<xs:schema xmlns = "http://www.imsglobal.org/xsd/imsss"
	 targetNamespace = "http://www.imsglobal.org/xsd/imsss"
	 xmlns:xs = "http://www.w3.org/2001/XMLSchema"
	 elementFormDefault = "qualified"
	 attributeFormDefault = "unqualified">
	<xs:include schemaLocation = "imsss_v1p0util.xsd"/>
	<xs:complexType name = "rollupRuleType">
		<xs:sequence>
			<xs:element name = "rollupConditions"
				 block = "#all">
				<xs:complexType>
					<xs:sequence>
						<xs:element name = "rollupCondition"
							 block = "#all" maxOccurs = "unbounded">
							<xs:complexType>
								<xs:attribute name = "operator" default = "noOp" type = "conditionOperatorType"/>
								<xs:attribute name = "condition" use = "required" type = "rollupRuleConditionType"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name = "conditionCombination" default = "any" type = "conditionCombinationType"/>
				</xs:complexType>
			</xs:element>
			<xs:element name = "rollupAction"
				 block = "#all">
				<xs:complexType>
					<xs:attribute name = "action" use = "required" type = "rollupActionType"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name = "childActivitySet" default = "all" type = "childActivityType"/>
		<xs:attribute name = "minimumCount" default = "0" type = "xs:nonNegativeInteger"/>
		<xs:attribute name = "minimumPercent" default = "0" type = "percentType"/>
	</xs:complexType>
	<xs:complexType name = "rollupRulesType">
		<xs:sequence>
			<xs:element name = "rollupRule" type = "rollupRuleType"
				 block = "#all" minOccurs = "0" maxOccurs = "unbounded"/>
		</xs:sequence>
		<xs:attribute name = "rollupObjectiveSatisfied" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "rollupProgressCompletion" default = "true" type = "xs:boolean"/>
		<xs:attribute name = "objectiveMeasureWeight" default = "1.0000" type = "weightType"/>
	</xs:complexType>
</xs:schema>